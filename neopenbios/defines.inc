/*
 * Copyright (c) 2011 <PERSON> <elbar<PERSON>@neogeodev.org>
 * Copyright (c) 2011 <PERSON> <<EMAIL>>
 *
 * Permission to use, copy, modify, and distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 */

/* $Id: defines.inc,v 1.16 2011/11/14 19:17:46 furrtek Exp $ */

REG_P1CNT =		0x300000
REG_DIPSW =		0x300001
REG_WATCHDOG =		0x300001
REG_TESTW =		0x300081
REG_SOUND =		0x320000
REG_STATUS_A =		0x320001
REG_P2CNT =		0x340000
REG_STATUS_B =		0x380000
REG_POUTPUT =		0x380001
REG_CRDBANK =		0x380011
REG_SLOT =		0x380021
REG_LEDLATCHES =	0x380031
REG_LEDDATA =		0x380041
REG_RTCCTRL =		0x380051

REG_NOSHADOW =		0x3A0001
REG_SHADOW =		0x3A0011
REG_SWPBIOS =		0x3A0003
REG_SWPROM =		0x3A0013
REG_CRDUNLOCK1 =	0x3A0005
REG_CRDLOCK1 =		0x3A0015
REG_CRDLOCK2 =		0x3A0007
REG_CRDUNLOCK2 =	0x3A0017
REG_CRDREGSEL =		0x3A0009
REG_CRDNORMAL =		0x3A0019
REG_BRDFIX =		0x3A000B
REG_CRTFIX =		0x3A001B
REG_SRAMLOCK =		0x3A000D
REG_SRAMUNLOCK =	0x3A001D
REG_PALBANK1 =		0x3A000F
REG_PALBANK0 =		0x3A001F

REG_VRAMADDR =		0x3C0000
REG_VRAMRW =		0x3C0002
REG_VRAMMOD =		0x3C0004
REG_LSPCMODE =		0x3C0006
REG_TIMERHIGH =		0x3C0008
REG_TIMERLOW =		0x3C000A
REG_IRQACK =		0x3C000C
REG_TIMERSTOP =		0x3C000E

BIOS_SYSTEM_MODE  =   0x10FD80
BIOS_MVS_FLAG     =   0x10FD82
BIOS_COUNTRY_CODE =   0x10FD83
BIOS_GAME_DIP     =   0x10FD84
BIOS_DEVMODE      =   0x10FE80

BIOS_P1CURRENT  =     0x10FD95
BIOS_P1PREVIOUS =     0x10FD96
BIOS_P1CHANGE   =     0x10FD97
BIOS_P1REPEAT   =     0x10FD98
BIOS_P1TIMER    =     0x10FD99

BIOS_USER_REQUEST =   0x10FDAE
BIOS_USER_MODE    =   0x10FDAF

BIOS_START_FLAG =     0x10FDB4

BIOS_FRAME_COUNTER =  0x10FEE4

INPUT_1 =	0x10FD94
INPUT_2 =	0x10FD9A
INPUT_3 =	0x10FDA0
INPUT_4 =	0x10FDA6
INPUT_S =	0x10FDAC

VSYNC_FLAG =	0x10FE8C

RAMSTART =	0x100000
PALETTES =      0x400000
BACKDROPCOLOR =	0x401FFE

FIXMAP =        0x7000
SCB1   =        0x0000
SCB2   =        0x8000
SCB3   =        0x8200
SCB4   =        0x8400

BLACK  =        0x8000
WHITE  =        0x7FFF
RED    =        0x4F00
GREEN  =        0x20F0
BLUE   =        0x100F

SRAM_COIN1 =	0xD00034
SRAM_COIN2 =	0xD00035

SRAM_FIRSTCOLOR          =      0xD00036
SRAM_SECONDCOLOR         =      0xD00038
SRAM_THIRDCOLOR          =      0xD00040

SRAM_COINS_P1_NEEDED     =	0xD0003A
SRAM_CREDITS_P1_ADDED    =	0xD0003B
SRAM_COINS_P2_NEEDED     =	0xD0003C
SRAM_CREDITS_P2_ADDED    =	0xD0003D

SRAM_SYSTEM              =      0xD0003E
SRAM_REGION              =      0xD0003F


SRAM_GAMESELECT          =      0xD00042
SRAM_STARTCOMPULSION     =      0xD00044
SRAM_SOUND_STOP          =      0xD00046

SRAM_LOGOCOLOR           =      0xD00048  | Warning: may be used by original BIOS !
SRAM_CUSTOMMESSAGE       =      0xD0FFF0  | To move !

CREDIT_DEC_P1		 =	0x10FDB0
CREDIT_DEC_P2		 =	0x10FDB1

FLAG_EYECATCH = 0x000114
USER          = 0x000122
PLAYER_START  = 0x000128
COIN_SOUND    = 0x000134

FLAG_INGAME   = 0x01

TEXTCOLOR     = 0x000F
