/*
 * @Author: wuqi_y <EMAIL>
 * @Date: 2025-07-29 19:15:41
 * @LastEditors: wuqi_y <EMAIL>
 * @LastEditTime: 2025-07-31 09:15:12
 * @Description: 
 * 
 */
import type { EmulatorCore } from '../types/emulator'

export const EMULATOR_CORES: EmulatorCore[] = [
  {
    id: 'gambatte',
    name: 'Game Boy / Game Boy Color',
    extensions: ['gb', 'gbc'],
    description: '任天堂Game Boy和Game Boy Color模拟器',
    cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/gambatte-wasm.data'
  },
  {
    id: 'mgba',
    name: 'Game Boy Advance',
    extensions: ['gba'],
    description: '任天堂Game Boy Advance模拟器',
    cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/mgba-wasm.data'
  },
  {
    id: 'snes9x',
    name: 'Super Nintendo',
    extensions: ['smc', 'sfc', 'swc'],
    description: '超级任天堂娱乐系统模拟器',
    cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/snes9x-wasm.data'
  },
  {
    id: 'fceumm',
    name: 'Nintendo Entertainment System',
    extensions: ['nes'],
    description: '任天堂红白机模拟器',
    cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/fceumm-wasm.data'
  },
  {
    id: 'genesis_plus_gx',
    name: 'Sega Genesis / Mega Drive',
    extensions: ['gen', 'bin'],
    description: '世嘉Genesis和Mega Drive模拟器',
    cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/genesis_plus_gx-wasm.data'
  },
  {
    id: 'smsplus',
    name: 'Sega Master System',
    extensions: ['sms'],
    description: '世嘉Master System模拟器',
    cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/smsplus-wasm.data'
  },
  {
    id: 'stella2014',
    name: 'Atari 2600',
    extensions: ['a26', 'bin'],
    description: 'Atari 2600游戏机模拟器',
    cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/stella2014-wasm.data'
  },
  {
    id: 'a5200',
    name: 'Atari 5200',
    extensions: ['a52', 'bin'],
    description: 'Atari 5200游戏机模拟器',
    cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/a5200-wasm.data'
  },
  {
    id: 'desmume2015',
    name: 'Nintendo DS',
    extensions: ['nds'],
    description: '任天堂DS掌机模拟器',
    cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/desmume2015-wasm.data'
  },
  {
    id: 'mupen64plus_next',
    name: 'Nintendo 64',
    extensions: ['n64', 'v64', 'z64'],
    description: '任天堂64游戏机模拟器',
    cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/mupen64plus_next-wasm.data'
  },
  {
    id: 'mednafen_psx_hw',
    name: 'Sony PlayStation',
    extensions: ['bin', 'cue', 'img', 'mdf', 'pbp', 'toc', 'cbn', 'm3u'],
    description: '索尼PlayStation游戏机模拟器',
    cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/mednafen_psx_hw-wasm.data'
  },
  {
    id: 'melonds',
    name: 'Nintendo DS (MelonDS)',
    extensions: ['nds'],
    description: 'Nintendo DS模拟器 - MelonDS核心',
    cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/melonds-wasm.data'
  },
  {
    id: 'desmume',
    name: 'Nintendo DS (DeSmuME)',
    extensions: ['nds'],
    description: 'Nintendo DS模拟器 - DeSmuME核心',
    cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/desmume-wasm.data'
  },
  {
    id: 'fbneo',
    name: 'Arcade (FBNeo)',
    extensions: ['zip', 'neo', 'mvs'],
    description: 'FinalBurn Neo街机模拟器 - 支持拳皇97等经典街机游戏',
    cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/fbneo-wasm.data'
  }
]

export function detectCoreByExtension(extension: string): EmulatorCore | null {
  const ext = extension.toLowerCase().replace('.', '')
  return EMULATOR_CORES.find(core =>
    core.extensions.includes(ext)
  ) || null
}

export function detectCoreBySystem(system: string): EmulatorCore | null {
  // 根据系统类型映射到核心
  const systemCoreMap: { [key: string]: string[] } = {
    nds: ['melonds', 'desmume'],
    gb: ['gambatte'],
    gbc: ['gambatte'],
    gba: ['mgba'],
    nes: ['fceumm'],
    snes: ['snes9x'],
    md: ['genesis_plus_gx'],
    n64: ['mupen64plus_next'],
    psx: ['mednafen_psx_hw'],
    arcade: ['fbneo'],
    neogeo: ['fbneo']
  }

  const coreIds = systemCoreMap[system.toLowerCase()]
  if (coreIds && coreIds.length > 0) {
    // 返回第一个匹配的核心
    return EMULATOR_CORES.find(core => core.id === coreIds[0]) || null
  }

  return null
}

export function getAllSupportedExtensions(): string[] {
  return EMULATOR_CORES.flatMap(core => core.extensions)
}