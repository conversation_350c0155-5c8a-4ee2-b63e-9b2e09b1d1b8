<template>
  <div class="w-full space-y-6">
    <!-- 预置 BIOS 下载 -->
    <PresetBiosDownloader />

    <!-- 分隔线 -->
    <div class="relative text-center">
      <div class="absolute inset-0 flex items-center">
        <div class="w-full border-t border-gray-600"></div>
      </div>
      <div class="relative flex justify-center text-sm">
        <span
          class="px-4 py-2 bg-gray-800 text-gray-400 rounded-full font-medium"
        >
          或者手动上传
        </span>
      </div>
    </div>

    <!-- BIOS Info -->
    <div class="card bg-blue-900/20 border-blue-500/30">
      <div class="p-4">
        <h3 class="text-lg font-semibold text-blue-400 mb-2 flex items-center">
          <span class="mr-2">🔧</span>
          BIOS 文件管理
        </h3>
        <p class="text-gray-300 text-sm mb-3">
          某些模拟器核心需要 BIOS 文件才能正常运行游戏。请上传对应的 BIOS 文件。
        </p>

        <!-- Required BIOS List -->
        <div class="space-y-2">
          <h4 class="text-sm font-medium text-gray-400">常用 BIOS 文件：</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
            <div class="flex items-center space-x-2">
              <span class="w-2 h-2 bg-red-500 rounded-full"></span>
              <span class="text-gray-300">neogeo.zip - Neo Geo 街机</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="w-2 h-2 bg-yellow-500 rounded-full"></span>
              <span class="text-gray-300">pgm.zip - PGM 街机</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="w-2 h-2 bg-green-500 rounded-full"></span>
              <span class="text-gray-300">scph1001.bin - PlayStation</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
              <span class="text-gray-300">dc_boot.bin - Dreamcast</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Upload Area -->
    <div
      class="card border-2 border-dashed border-purple-600 hover:border-purple-500 transition-colors duration-300 cursor-pointer group"
      @drop="handleDrop"
      @dragover.prevent
      @dragenter.prevent
      @click="$refs.fileInput?.click()"
    >
      <div class="p-6 text-center space-y-3">
        <div
          class="text-4xl mb-2 group-hover:scale-110 transition-transform duration-300"
        >
          🔧
        </div>
        <h3 class="text-lg font-semibold text-white mb-1">
          将 BIOS 文件拖拽到这里
        </h3>
        <p class="text-gray-400 text-sm mb-4">支持 .zip, .bin, .rom 等格式</p>

        <input
          ref="fileInput"
          type="file"
          multiple
          accept=".zip,.bin,.rom,.bios"
          @change="handleFileSelect"
          class="hidden"
        />

        <div class="text-xs text-gray-500">
          文件将安全存储在浏览器本地存储中
        </div>
      </div>
    </div>

    <!-- Uploaded BIOS Files -->
    <div v-if="uploadedBiosFiles.length > 0" class="card">
      <div class="p-4">
        <h3 class="text-lg font-semibold text-white mb-3 flex items-center">
          <span class="mr-2">📁</span>
          已上传的 BIOS 文件
        </h3>

        <div class="space-y-2">
          <div
            v-for="bios in uploadedBiosFiles"
            :key="bios.name"
            class="flex items-center justify-between p-3 bg-gray-800 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <div
                class="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center"
              >
                <span class="text-xs font-bold text-white">
                  {{ bios.name.split(".").pop()?.toUpperCase() }}
                </span>
              </div>
              <div>
                <div class="text-sm font-medium text-white">
                  {{ bios.name }}
                </div>
                <div class="text-xs text-gray-400">
                  {{ formatFileSize(bios.size) }} • {{ bios.system || "通用" }}
                </div>
              </div>
            </div>

            <button
              @click="removeBiosFile(bios.name)"
              class="text-red-400 hover:text-red-300 transition-colors p-1"
              title="删除 BIOS 文件"
            >
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fill-rule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Processing Indicator -->
    <div v-if="isProcessing" class="card bg-purple-900/20 border-purple-500/30">
      <div class="p-4 text-center">
        <div class="inline-flex items-center space-x-2">
          <div
            class="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-500"
          ></div>
          <span class="text-purple-400">正在处理 BIOS 文件...</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import {
  storeBiosFile,
  getAllBiosFiles,
  removeBiosFile as deleteBiosFile,
} from "../utils/biosStorage";
import toast from "../utils/toast";
import PresetBiosDownloader from "./PresetBiosDownloader.vue";

interface BiosFile {
  name: string;
  data: ArrayBuffer;
  size: number;
  system?: string;
}

const isProcessing = ref(false);
const uploadedBiosFiles = ref<BiosFile[]>([]);

// 加载已存储的 BIOS 文件
onMounted(async () => {
  try {
    uploadedBiosFiles.value = await getAllBiosFiles();
  } catch (error) {
    console.error("加载 BIOS 文件失败:", error);
  }
});

// 格式化文件大小
function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// 检测 BIOS 文件类型
function detectBiosSystem(filename: string): string {
  const name = filename.toLowerCase();

  if (name.includes("neogeo")) return "Neo Geo";
  if (name.includes("pgm")) return "PGM";
  if (name.includes("scph") || name.includes("playstation"))
    return "PlayStation";
  if (name.includes("dc_") || name.includes("dreamcast")) return "Dreamcast";
  if (name.includes("saturn")) return "Saturn";
  if (name.includes("32x")) return "32X";
  if (name.includes("sega")) return "Sega";
  if (name.includes("nintendo") || name.includes("nes")) return "Nintendo";

  return "通用";
}

// 处理文件上传
async function processFiles(files: FileList) {
  if (files.length === 0) return;

  isProcessing.value = true;
  try {
    for (const file of Array.from(files)) {
      try {
        const arrayBuffer = await file.arrayBuffer();
        const biosFile: BiosFile = {
          name: file.name,
          data: arrayBuffer,
          size: arrayBuffer.byteLength,
          system: detectBiosSystem(file.name),
        };

        // 存储 BIOS 文件
        await storeBiosFile(biosFile);
        uploadedBiosFiles.value.push(biosFile);

        toast.success(`BIOS 文件 ${file.name} 上传成功`);
      } catch (error) {
        console.error(`处理文件 ${file.name} 失败:`, error);
        toast.error(`处理文件 ${file.name} 失败`);
      }
    }
  } catch (error) {
    console.error("处理 BIOS 文件失败:", error);
    toast.error("处理 BIOS 文件失败");
  } finally {
    isProcessing.value = false;
  }
}

// 删除 BIOS 文件
async function removeBiosFile(filename: string) {
  try {
    await deleteBiosFile(filename);
    uploadedBiosFiles.value = uploadedBiosFiles.value.filter(
      (f) => f.name !== filename
    );
    toast.success(`BIOS 文件 ${filename} 已删除`);
  } catch (error) {
    console.error("删除 BIOS 文件失败:", error);
    toast.error("删除 BIOS 文件失败");
  }
}

function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement;
  if (target.files) {
    processFiles(target.files);
  }
}

function handleDrop(event: DragEvent) {
  event.preventDefault();
  if (event.dataTransfer?.files) {
    processFiles(event.dataTransfer.files);
  }
}
</script>
