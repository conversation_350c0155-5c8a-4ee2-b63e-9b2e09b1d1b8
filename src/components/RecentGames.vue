<template>
  <div v-if="recentGames.length > 0" class="space-y-4">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div
        v-for="game in recentGames"
        :key="game.id"
        @click="playGame(game)"
        class="group cursor-pointer card p-4 hover:scale-105 transition-all duration-300 hover:border-blue-500/50 hover:shadow-lg hover:shadow-blue-500/20"
      >
        <!-- 游戏图标和信息 -->
        <div class="flex items-center gap-4 mb-3">
          <div class="text-3xl">🎮</div>
          <div class="flex-1 min-w-0">
            <!-- 游戏名称 - 支持编辑 -->
            <div v-if="editingGameId === game.id" class="mb-1">
              <input
                ref="editInput"
                v-model="editingName"
                @blur="saveGameName(game.id)"
                @keyup.enter="saveGameName(game.id)"
                @keyup.escape="cancelEdit"
                class="w-full bg-gray-700 text-white text-lg font-semibold px-2 py-1 rounded border border-blue-500 focus:outline-none focus:border-blue-400"
                @click.stop
              />
            </div>
            <h3
              v-else
              @dblclick="startEdit(game)"
              class="text-lg font-semibold text-white mb-1 whitespace-nowrap overflow-hidden text-ellipsis cursor-pointer hover:text-blue-300 transition-colors"
              title="双击重命名"
            >
              {{ game.name }}
            </h3>
            <div class="flex items-center gap-2 text-sm text-gray-400">
              <span class="uppercase font-medium">.{{ game.extension }}</span>
              <span>•</span>
              <span>{{ formatFileSize(game.size) }}</span>
            </div>
          </div>
        </div>

        <!-- 最后游玩时间和操作按钮 -->
        <div class="flex items-center justify-between text-xs text-gray-500">
          <span>{{ formatTimestamp(game.timestamp) }}</span>
          <div
            class="opacity-0 group-hover:opacity-100 transition-opacity relative z-10 flex gap-1"
          >
            <button
              @click.stop="startEdit(game)"
              class="text-blue-400 hover:text-blue-300 p-1 rounded hover:bg-blue-500/20 transition-colors"
              title="重命名游戏"
            >
              <svg
                class="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                ></path>
              </svg>
            </button>
            <button
              @click.stop="deleteGame(game.id)"
              class="text-red-400 hover:text-red-300 p-1 rounded hover:bg-red-500/20 transition-colors"
              title="删除游戏"
            >
              <svg
                class="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                ></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- 播放按钮覆盖层 -->
        <div
          class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/20 rounded-2xl pointer-events-none"
        >
          <div class="bg-blue-500 text-white p-3 rounded-full shadow-lg">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z" />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 清空所有游戏按钮 -->
    <div class="flex justify-end pt-4">
      <button
        @click="clearAllGames"
        class="text-sm text-gray-400 hover:text-red-400 transition-colors flex items-center gap-2"
      >
        <svg
          class="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
          ></path>
        </svg>
        清空所有游戏
      </button>
    </div>
  </div>

  <!-- 空状态 -->
  <div v-else class="text-center py-12">
    <div class="text-6xl mb-4">🎮</div>
    <h3 class="text-xl font-semibold text-gray-300 mb-2">还没有游戏记录</h3>
    <p class="text-gray-500">上传或选择游戏开始你的游戏之旅吧！</p>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import { useRouter } from "vue-router";
import { gameStorage, type StoredGame } from "../utils/gameStorage";
import { formatFileSize } from "../utils/fileHandler";
import toast from "../utils/toast";

const router = useRouter();
const recentGames = ref<StoredGame[]>([]);
const editingGameId = ref<string | null>(null);
const editingName = ref<string>("");
const editInput = ref<HTMLInputElement>();

const emit = defineEmits<{
  gameDeleted: [];
  gameRenamed: [];
}>();

onMounted(async () => {
  await loadRecentGames();
});

async function loadRecentGames() {
  try {
    const games = await gameStorage.getRecentGames(6); // 显示最近6个游戏
    recentGames.value = games;
  } catch (error) {
    console.error("加载最近游戏失败:", error);
    toast.error("加载最近游戏失败");
  }
}

async function playGame(game: StoredGame) {
  try {
    // 更新游戏的时间戳
    await gameStorage.updateGameTimestamp(game.id);

    // 设置当前游戏ID
    sessionStorage.setItem("selectedGameId", game.id);

    // 跳转到游戏页面
    router.push("/play");

    toast.success(`正在启动 ${game.name}`);
  } catch (error) {
    console.error("启动游戏失败:", error);
    toast.error("启动游戏失败，请重试");
  }
}

async function deleteGame(gameId: string) {
  try {
    await gameStorage.deleteGame(gameId);
    await loadRecentGames(); // 重新加载列表
    emit("gameDeleted");
    toast.success("游戏已删除");
  } catch (error) {
    console.error("删除游戏失败:", error);
    toast.error("删除游戏失败");
  }
}

async function clearAllGames() {
  if (confirm("确定要清空所有游戏吗？此操作不可撤销。")) {
    try {
      const allGames = await gameStorage.getAllGames();
      for (const game of allGames) {
        await gameStorage.deleteGame(game.id);
      }
      await loadRecentGames();
      emit("gameDeleted");
      toast.success("所有游戏已清空");
    } catch (error) {
      console.error("清空游戏失败:", error);
      toast.error("清空游戏失败");
    }
  }
}

async function startEdit(game: StoredGame) {
  editingGameId.value = game.id;
  editingName.value = game.name;

  // 等待DOM更新后聚焦输入框并选中文本
  await nextTick();
  if (editInput.value) {
    editInput.value.focus();
    editInput.value.select();
  }
}

function cancelEdit() {
  editingGameId.value = null;
  editingName.value = "";
}

async function saveGameName(gameId: string) {
  if (!editingName.value.trim()) {
    toast.error("游戏名称不能为空");
    return;
  }

  try {
    await gameStorage.updateGameName(gameId, editingName.value.trim());
    await loadRecentGames(); // 重新加载列表
    emit("gameRenamed");
    toast.success("游戏重命名成功");
    cancelEdit();
  } catch (error) {
    console.error("重命名游戏失败:", error);
    toast.error("重命名游戏失败");
  }
}

function formatTimestamp(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;

  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) {
    return "刚刚";
  } else if (minutes < 60) {
    return `${minutes}分钟前`;
  } else if (hours < 24) {
    return `${hours}小时前`;
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return new Date(timestamp).toLocaleDateString("zh-CN");
  }
}

// 暴露刷新方法给父组件
defineExpose({
  loadRecentGames,
});
</script>

<style scoped>
.card {
  position: relative;
  overflow: hidden;
}
</style>
