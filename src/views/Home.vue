<template>
  <div class="min-h-screen flex flex-col">
    <!-- Hero Section -->
    <section class="flex-1 flex flex-col justify-center items-center text-center px-4 py-16">
      <div class="max-w-4xl mx-auto space-y-8">
        <!-- 主标题 -->
        <h1 class="text-5xl md:text-7xl font-bold text-gradient animate-float">
          🎮 游戏模拟器
        </h1>
        
        <!-- 副标题 -->
        <p class="text-xl md:text-2xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
          在浏览器中畅玩经典游戏！支持 Game Boy、GBA、NES、SNES 等多种游戏机
        </p>
        
        <!-- 特性介绍 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
          <div class="card p-6 text-center">
            <div class="text-4xl mb-4">🚀</div>
            <h3 class="text-lg font-semibold mb-2">即开即玩</h3>
            <p class="text-gray-400 text-sm">无需下载安装，直接在浏览器中运行</p>
          </div>
          
          <div class="card p-6 text-center">
            <div class="text-4xl mb-4">🎯</div>
            <h3 class="text-lg font-semibold mb-2">多平台支持</h3>
            <p class="text-gray-400 text-sm">支持多种经典游戏机平台和格式</p>
          </div>
          
          <div class="card p-6 text-center">
            <div class="text-4xl mb-4">📱</div>
            <h3 class="text-lg font-semibold mb-2">移动端优化</h3>
            <p class="text-gray-400 text-sm">完美适配手机和平板设备</p>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mt-12">
          <router-link 
            to="/library" 
            class="btn-primary text-lg px-8 py-4 animate-bounce-slow hover:animate-none"
          >
            🎮 开始游戏
          </router-link>
          
          <router-link 
            to="/about" 
            class="btn-secondary text-lg px-8 py-4"
          >
            📖 了解更多
          </router-link>
        </div>
      </div>
    </section>
    
    <!-- 快速开始 -->
    <section class="bg-black/20 backdrop-blur-sm border-t border-white/10 py-8">
      <div class="max-w-4xl mx-auto px-4 text-center">
        <h2 class="text-2xl font-bold mb-4">快速开始</h2>
        <p class="text-gray-300 mb-6">
          上传你的游戏文件或选择内置游戏，立即开始游戏体验
        </p>
        <div class="flex flex-wrap justify-center gap-4 text-sm">
          <span class="bg-primary-500/20 text-primary-300 px-3 py-1 rounded-full">
            .gb / .gbc
          </span>
          <span class="bg-primary-500/20 text-primary-300 px-3 py-1 rounded-full">
            .gba
          </span>
          <span class="bg-primary-500/20 text-primary-300 px-3 py-1 rounded-full">
            .nes
          </span>
          <span class="bg-primary-500/20 text-primary-300 px-3 py-1 rounded-full">
            .snes / .smc
          </span>
          <span class="bg-primary-500/20 text-primary-300 px-3 py-1 rounded-full">
            .zip
          </span>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// 页面组件逻辑
</script>

<style scoped>
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-bounce-slow {
  animation: bounce-slow 2s ease-in-out infinite;
}
</style>
