<template>
  <div class="min-h-screen flex flex-col mobile-safe-area">
    <!-- 游戏信息和返回按钮 -->
    <div
      v-if="currentGame && !isFullscreen"
      class="p-4 bg-black/20 backdrop-blur-sm border-b border-white/10"
    >
      <div class="max-w-6xl mx-auto flex items-center justify-between">
        <div class="flex items-center gap-4">
          <router-link
            to="/library"
            class="btn-secondary flex items-center gap-2"
          >
            <span>←</span>
            返回游戏库
          </router-link>

          <div v-if="currentGame">
            <h1 class="text-xl font-bold">{{ currentGame.name }}</h1>
            <p class="text-sm text-gray-400">{{ currentGame.system }}</p>
          </div>
        </div>

        <div v-if="selectedCore" class="text-sm text-gray-400">
          核心: {{ selectedCore.name }}
        </div>
      </div>
    </div>

    <!-- 主要游戏区域 -->
    <main class="flex-1 flex flex-col">
      <!-- 没有游戏时显示选择提示 -->
      <div
        v-if="!currentGame"
        class="flex-1 flex items-center justify-center p-8"
      >
        <div class="text-center space-y-6">
          <div class="text-6xl">🎮</div>
          <h2 class="text-2xl font-bold">没有选择游戏</h2>
          <p class="text-gray-400">请先选择一个游戏开始游戏</p>
          <router-link
            to="/library"
            class="btn-primary inline-flex items-center gap-2"
          >
            <span>🎯</span>
            选择游戏
          </router-link>
        </div>
      </div>

      <!-- 有游戏但未开始游戏时显示核心选择 -->
      <div v-else-if="!isPlaying" class="flex-1 p-4 md:p-8">
        <div class="max-w-4xl mx-auto space-y-6">
          <GameInfo :game="currentGame" :core="selectedCore" @back="goBack" />

          <CoreSelector
            :game="currentGame"
            :selected-core="selectedCore"
            :game-system="currentGame.system"
            @core-selected="handleCoreSelected"
            @play="startGame"
          />
        </div>
      </div>

      <!-- 游戏进行中 -->
      <div v-else-if="currentGame && selectedCore" class="flex-1">
        <RetroArchEmulator
          :game="currentGame"
          :core="selectedCore"
          :loading="isLoading"
          @error="handleError"
          @stop="stopGame"
          @back="stopGame"
        />
      </div>
    </main>

    <!-- 错误提示 -->
    <div v-if="error" class="fixed bottom-4 right-4 max-w-md z-50">
      <div
        class="bg-red-500/20 border border-red-500/30 backdrop-blur-md rounded-xl p-4 text-white shadow-2xl"
      >
        <div class="flex items-start gap-3">
          <span class="text-red-400 text-xl">❌</span>
          <div class="flex-1">
            <p class="text-sm">{{ error }}</p>
            <button
              @click="error = null"
              class="mt-2 text-xs bg-red-500/30 hover:bg-red-500/50 px-3 py-1 rounded-lg transition-colors"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import type { GameFile, EmulatorCore } from "../types/emulator";
import GameInfo from "../components/GameInfo.vue";
import CoreSelector from "../components/CoreSelector.vue";
import RetroArchEmulator from "../components/RetroArchEmulator.vue";
import { gameStorage } from "../utils/gameStorage";

const router = useRouter();

const currentGame = ref<GameFile | null>(null);
const selectedCore = ref<EmulatorCore | null>(null);
const isPlaying = ref(false);
const isLoading = ref(false);
const isFullscreen = ref(false);
const error = ref<string | null>(null);

onMounted(async () => {
  // 从 sessionStorage 获取游戏 ID
  const gameId = sessionStorage.getItem("selectedGameId");
  console.log("从 sessionStorage 获取的游戏 ID:", gameId);

  if (gameId) {
    try {
      // 从 IndexedDB 获取游戏数据
      const game = await gameStorage.getGame(gameId);
      if (game) {
        currentGame.value = game;
        console.log(
          "从 IndexedDB 加载游戏成功:",
          game.name,
          "大小:",
          game.size,
          "字节"
        );

        // 更新游戏的最后游玩时间
        try {
          await gameStorage.updateGameTimestamp(gameId);
          console.log("游戏时间戳已更新");
        } catch (err) {
          console.warn("更新游戏时间戳失败:", err);
        }
      } else {
        console.log("未找到游戏数据，重定向到游戏库");
        router.push("/library");
      }
    } catch (err) {
      console.error("从 IndexedDB 加载游戏失败:", err);
      error.value = "加载游戏数据失败";
      router.push("/library");
    }
  } else {
    console.log("没有找到游戏 ID，重定向到游戏库");
    router.push("/library");
  }
});

function handleCoreSelected(core: EmulatorCore) {
  selectedCore.value = core;
}

function startGame() {
  if (currentGame.value && selectedCore.value) {
    isPlaying.value = true;
    isLoading.value = true;
  }
}

function stopGame() {
  isPlaying.value = false;
  isLoading.value = false;
}

function goBack() {
  router.push("/library");
}

function handleError(errorMessage: string) {
  error.value = errorMessage;
  isLoading.value = false;
}
</script>
